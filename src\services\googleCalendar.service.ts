import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { GoogleCalendarDao } from "../lib/dao/googleCalendar.dao";
import { google } from "googleapis";
import { throwError } from "../util/response";
import { CONFIG } from "../config/environment";
import { TherapistDao } from "../lib/dao/therapist.dao";
import { v4 as uuidv4 } from "uuid";
import { Utility } from "../util/util";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { ClientService } from "./client.service";
import moment from "moment";
import ScheduleModel, { ScheduleStatus } from "../models/Schedule.model";

import { Types } from "mongoose";
import { PayTrackerService } from "./payTracker.service";
import {
  PaymentTrackerStatusEnum,
  PaymentTrackerTypeEnum,
} from "../models/PayTracker.model";
import calendarEventModel from "../models/calendarEvent.model";
import createScheduleTemplate from "../util/emailTemplate/create.schedule";
import { Mailer2 } from "../util/mailer2";
import { ScheduleService } from "./schedule.service";
import { isOverlap, findOverlaps } from "../helper/custom.helper";
import { MailSubjectEnum } from "../lib/enum/subject.enum";

export interface IAddEventPayload {
  emails: string[];
  summary: string;
  location: string;
  description: string;
}

export class GoogleCalendarService {
  static async create(payload: any) {
    return await GoogleCalendarDao.create(payload);
  }

  static async update(_id: any, payload: any) {
    return await GoogleCalendarDao.update(_id, payload);
  }

  static async findByTherapist(therapist: any) {
    return await GoogleCalendarDao.findByTherapist(therapist);
  }

  static async findby_id(_id: any) {
    return await CalendarEventDao.findby_id(_id);
  }

  static async createEvent(event: any) {
    return await GoogleCalendarDao.createEvent(event);
  }

  static async CheckEventLocally(eventIds: any) {
    const ids = await CalendarEventDao.CheckEventLocally(eventIds);
    const eventId = ids.map((data: any) => {
      return data.id;
    });
    return eventId;
  }

  static async addEventToCalender(
    therapistId: any,
    data: IAddEventPayload,
    recurrenceDate: {
      toDate: any;
      fromDate: any;
      _id: any;
      rrule?: any;
      data?: any;
    },
    scheduleId: any
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    const therapist: any = await TherapistDao.getTherapist(therapistId);

    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    // Assuming you're storing tokens in session after authentication
    if (tokens) {
      oauth2Client.setCredentials(tokens);

      const calendar: any = google.calendar({
        version: "v3",
        auth: oauth2Client,
      });
      const emails = data.emails.map((email: string) => {
        return {
          email: email,
        };
      });
      emails.push({
        email: therapist.email,
      });
      const finalLinkData: any = {};
      // const recurrenceDate: any = await Utility.findNearestFromDate(schedule);
      // finalLinkData.recurrenceDate = recurrenceDate;
      const event = {
        summary: data.summary,
        location: data.location,
        description: data.description,
        start: {
          dateTime: recurrenceDate.fromDate,
          timeZone: "IST",
        },
        end: {
          dateTime: recurrenceDate.toDate,
          timeZone: "IST",
        },
        attendees: emails, // [{ email: '<EMAIL>' }]
        recurrence: recurrenceDate.rrule ? [recurrenceDate.rrule] : [],
        conferenceData: {
          createRequest: {
            requestId: uuidv4(), // Generate a random string for the requestId
            conferenceSolutionKey: {
              type: "hangoutsMeet", // Use 'hangoutsMeet' for Google Meet
            },
          },
        },
      };
      // Check if this is a recurring event
      const isRecurring =
        recurrenceDate.rrule && recurrenceDate.rrule.length > 0;

      // Create the event in Google Calendar
      const createdEvent = await calendar.events.insert({
        calendarId: "primary",
        resource: event,
        conferenceDataVersion: 1,
        requestBody: event,
      });

      finalLinkData.link = createdEvent.data.hangoutLink;

      const masterEventId = createdEvent.data.id.split('_')[0];

      // updating the description of the master event
      await this.updateEventDescriptions(therapistId, [masterEventId]);

      if (
        isRecurring &&
        recurrenceDate.data &&
        recurrenceDate.data.length > 0
      ) {
        try {
          // Wait a moment for Google Calendar to process the recurring event
          await new Promise((resolve) => setTimeout(resolve, 2000));

          // Try to get instances of the recurring event
          const instances = await calendar.events.instances({
            calendarId: "primary",
            eventId: createdEvent.data.id,
          });

          if (instances.data.items && instances.data.items.length > 0) {
            // Create an array to store all calendar events
            const calendarEvents = [];

            // Process each instance and match it with a recurrence date
            for (let i = 0; i < instances.data.items.length; i++) {
              const instance = instances.data.items[i];

              // Find the matching recurrence date by comparing dates
              const instanceDate = moment(instance.start.dateTime).format(
                "YYYY-MM-DD"
              );

              // Find the matching recurrence date
              const matchingRecDate = recurrenceDate.data.find((rd: any) => {
                const rdDate = moment(rd.fromDate).format("YYYY-MM-DD");
                return rdDate === instanceDate;
              });

              // If we found a matching recurrence date, use its ID
              // Otherwise, use the ID of the recurrence date at the same index (or the first one if out of bounds)
              const scheduleRecId = matchingRecDate
                ? matchingRecDate._id
                : recurrenceDate.data[i]
                ? recurrenceDate.data[i]._id
                : recurrenceDate.data[0]._id;

              // Create the calendar event in the database
              const calendarEvent = await GoogleCalendarDao.createEvent({
                ...instance,
                scheduleId: scheduleId,
                therapistId: therapistId,
                scheduleRecId: scheduleRecId,
              });

              calendarEvents.push(calendarEvent);
            }

            // If we didn't get enough instances, create individual events for the missing dates
            if (instances.data.items.length < recurrenceDate.data.length) {
              // Find which recurrence dates don't have matching instances
              const processedDates = new Set(
                instances.data.items.map((item: any) =>
                  moment(item.start.dateTime).format("YYYY-MM-DD")
                )
              );

              const missingDates = recurrenceDate.data.filter(
                (rd: any) =>
                  !processedDates.has(moment(rd.fromDate).format("YYYY-MM-DD"))
              );

              // Create individual events for each missing date
              for (const rd of missingDates) {
                // Create a new event with the same details but different date
                const individualEvent = {
                  summary: event.summary,
                  location: event.location,
                  description: event.description,
                  start: {
                    dateTime: rd.fromDate,
                    timeZone: "IST",
                  },
                  end: {
                    dateTime: rd.toDate,
                    timeZone: "IST",
                  },
                  attendees: event.attendees,
                  conferenceData: {
                    createRequest: {
                      requestId: uuidv4(),
                      conferenceSolutionKey: {
                        type: "hangoutsMeet",
                      },
                    },
                  },
                };

                // Create the event in Google Calendar
                const individualCreatedEvent = await calendar.events.insert({
                  calendarId: "primary",
                  resource: individualEvent,
                  conferenceDataVersion: 1,
                  requestBody: individualEvent,
                });

                // Save the event in the database
                const individualCalendarEvent =
                  await GoogleCalendarDao.createEvent({
                    ...individualCreatedEvent.data,
                    scheduleId: scheduleId,
                    therapistId: therapistId,
                    scheduleRecId: rd._id,
                  });

                calendarEvents.push(individualCalendarEvent);
              }
            }

            finalLinkData.calenderEventId = calendarEvents;
          } else {
            // Create an array to store all calendar events
            const calendarEvents = [];

            // First, save the master event
            const masterEvent = await GoogleCalendarDao.createEvent({
              ...createdEvent.data,
              scheduleId: scheduleId,
              therapistId: therapistId,
              scheduleRecId: recurrenceDate.data[0]._id,
            });

            calendarEvents.push(masterEvent);

            // Create individual events for each recurrence date (except the first one which is already created)
            for (let i = 1; i < recurrenceDate.data.length; i++) {
              const rd = recurrenceDate.data[i];

              // Create a new event with the same details but different date
              const individualEvent = {
                summary: event.summary,
                location: event.location,
                description: event.description,
                start: {
                  dateTime: rd.fromDate,
                  timeZone: "IST",
                },
                end: {
                  dateTime: rd.toDate,
                  timeZone: "IST",
                },
                attendees: event.attendees,
                conferenceData: {
                  createRequest: {
                    requestId: uuidv4(),
                    conferenceSolutionKey: {
                      type: "hangoutsMeet",
                    },
                  },
                },
              };

              // Create the event in Google Calendar
              const individualCreatedEvent = await calendar.events.insert({
                calendarId: "primary",
                resource: individualEvent,
                conferenceDataVersion: 1,
                requestBody: individualEvent,
              });

              // Save the event in the database
              const individualCalendarEvent =
                await GoogleCalendarDao.createEvent({
                  ...individualCreatedEvent.data,
                  scheduleId: scheduleId,
                  therapistId: therapistId,
                  scheduleRecId: rd._id,
                });

              calendarEvents.push(individualCalendarEvent);
            }

            finalLinkData.calenderEventId = calendarEvents;
          }
        } catch (error) {
          console.error("ERROR: Error handling recurring event:", error);

          // Create an array to store all calendar events
          const calendarEvents = [];

          // First, save the master event
          const masterEvent = await GoogleCalendarDao.createEvent({
            ...createdEvent.data,
            scheduleId: scheduleId,
            therapistId: therapistId,
            scheduleRecId: recurrenceDate.data[0]._id,
          });

          calendarEvents.push(masterEvent);

          // Create individual events for each recurrence date (except the first one which is already created)
          for (let i = 1; i < recurrenceDate.data.length; i++) {
            const rd = recurrenceDate.data[i];

            // Create a new event with the same details but different date
            const individualEvent = {
              summary: event.summary,
              location: event.location,
              description: event.description,
              start: {
                dateTime: rd.fromDate,
                timeZone: "IST",
              },
              end: {
                dateTime: rd.toDate,
                timeZone: "IST",
              },
              attendees: event.attendees,
              conferenceData: {
                createRequest: {
                  requestId: uuidv4(),
                  conferenceSolutionKey: {
                    type: "hangoutsMeet",
                  },
                },
              },
            };

            // Create the event in Google Calendar
            const individualCreatedEvent = await calendar.events.insert({
              calendarId: "primary",
              resource: individualEvent,
              conferenceDataVersion: 1,
              requestBody: individualEvent,
            });

            // Save the event in the database
            const individualCalendarEvent = await GoogleCalendarDao.createEvent(
              {
                ...individualCreatedEvent.data,
                scheduleId: scheduleId,
                therapistId: therapistId,
                scheduleRecId: rd._id,
              }
            );

            calendarEvents.push(individualCalendarEvent);
          }

          finalLinkData.calenderEventId = calendarEvents;
        }
      } else {
        const calenderEvent = await GoogleCalendarDao.createEvent({
          ...createdEvent.data,
          scheduleId: scheduleId,
          therapistId: therapistId,
          scheduleRecId: recurrenceDate._id,
        });
        finalLinkData.calenderEventId = [calenderEvent];
      }

      return finalLinkData;
    } else {
      throw new Error("Authentication tokens are not available");
    }
  }

  static async addSingleEventToCalender(
    therapistId: any,
    data: IAddEventPayload,
    recurrenceDate: {
      toDate: any;
      fromDate: any;
      _id: any;
      rrule?: any;
      data?: any;
    },
    scheduleId: any
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    const therapist: any = await TherapistDao.getTherapist(therapistId);

    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    // Assuming you're storing tokens in session after authentication
    if (tokens) {
      oauth2Client.setCredentials(tokens);

      const calendar: any = google.calendar({
        version: "v3",
        auth: oauth2Client,
      });
      const emails = data.emails.map((email: string) => {
        return {
          email: email,
        };
      });
      emails.push({
        email: therapist.email,
      });
      const finalLinkData: any = {};
      // const recurrenceDate: any = await Utility.findNearestFromDate(schedule);
      // finalLinkData.recurrenceDate = recurrenceDate;
      const event = {
        summary: data.summary,
        location: data.location,
        description: data.description,
        start: {
          dateTime: recurrenceDate.fromDate,
          timeZone: "IST",
        },
        end: {
          dateTime: recurrenceDate.toDate,
          timeZone: "IST",
        },
        attendees: emails, // [{ email: '<EMAIL>' }]
        recurrence: recurrenceDate.rrule ? [recurrenceDate.rrule] : [],
        conferenceData: {
          createRequest: {
            requestId: uuidv4(), // Generate a random string for the requestId
            conferenceSolutionKey: {
              type: "hangoutsMeet", // Use 'hangoutsMeet' for Google Meet
            },
          },
        },
      };
      const createdEvent = await calendar.events.insert({
        calendarId: "primary",
        resource: event,
        conferenceDataVersion: 1, // Required to include conferenceData
        // sendNotifications: true, // If you want to send email notifications to attendees
        requestBody: event, // The event details
      });
      finalLinkData.link = createdEvent.data.hangoutLink;

      // const calenderEvent = await GoogleCalendarDao.createEvent(createEventArr);
      const calenderEvent = await GoogleCalendarDao.createEvent({
        ...createdEvent.data,
        scheduleId: scheduleId,
        therapistId: therapistId,
        scheduleRecId: recurrenceDate._id,
      });
      finalLinkData.calenderEventId = calenderEvent._id;

      return finalLinkData;
    } else {
      throw new Error("Authentication tokens are not available");
    }
  }

  static async getEventById(eventId: any) {
    return await CalendarEventDao.getEventById(eventId);
  }

  static async removeEventFromCalendar(
    therapistId: any,
    recurrenceData: any,
    deleteEntireSeries: boolean = false
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    const calendarEvent = await CalendarEventDao.findByTherapistAndRecId(
      therapistId,
      recurrenceData._id,
      recurrenceData.calenderEventId
    );

    // Remove the event from recurrence in the database
    await ScheduleDao.removeEventFromRecurrence(
      therapistId,
      recurrenceData._id
    );

    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    // Assuming you're storing tokens in session after authentication
    if (tokens) {
      oauth2Client.setCredentials(tokens);

      const calendar: any = google.calendar({
        version: "v3",
        auth: oauth2Client,
      });
      // await calendar.events.delete({
      //   calendarId: "primary",
      //   eventId: calendarEvent?.id, // Replace with the ID of the event you want to delete.
      // });
      try {
        if (deleteEntireSeries) {
          const calenderEventData = await GoogleCalendarService.getEventById(
            recurrenceData.calenderEventId
          );

          if (!calenderEventData) {
            return throwError("No Calender Event Data found.", 404);
          }

          // First get the event to check if it's a recurring event
          // const event = await calendar.events.get({
          //   calendarId: "primary",
          //   eventId: calendarEvent?.id,
          // });

          // If it's a recurring event instance, use the recurringEventId (master event id)
          // Otherwise use the current event id (it might be the master event already)
          const masterEventId = calenderEventData?.id.split("_")[0];

          await calendar.events.delete({
            calendarId: "primary",
            eventId: masterEventId,
          });
        } else {
          // Delete single instance
          await calendar.events.delete({
            calendarId: "primary",
            eventId: calendarEvent?.id,
          });
        }
      } catch (error) {
        console.error("Error deleting event:", error);
        throw error;
      }
    } else {
      throw new Error("Authentication tokens are not available");
    }
  }

  static async syncCalendarLocally(therapistId: any, eventIds: any) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const old_schedules = await ScheduleService.getScheduleByTherapistId(
      therapistId
    );
    let existing_dates = [];
    for (const old_schedule of old_schedules) {
      for (const old_date of old_schedule.recurrenceDates) {
        if (old_date.status != ScheduleStatus.CANCELLED) {
          existing_dates.push({
            fromDate: moment(old_date.fromDate),
            toDate: moment(old_date.toDate),
            clientName: old_schedule.name || 'Unknown Client',
            clientId: old_schedule.clientId,
            scheduleName: old_schedule.name
          });
        }
      }
    }

    const therapist = await TherapistDao.getTherapist(therapistId);
    if (!therapist) {
      return throwError("No Therapist found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    if (!tokens) {
      return throwError("No Google Calender Data found.", 404);
    }
    oauth2Client.setCredentials(tokens);

    let existing_events = await CalendarEventDao.getTherapistCalendarEvents(
      therapistId
    );

    let existing_event_ids = existing_events.map((event) => event.id);

    let not_existing_ids = eventIds.filter(
      (id: any) => !existing_event_ids.includes(id)
    );

    if (not_existing_ids.length == 0) {
      return throwError("All events are already synced.", 404);
    }

    const calendar = google.calendar({ version: "v3", auth: oauth2Client });

    await GoogleCalendarService.updateEventDescriptions(therapistId, eventIds);

    const events = await Promise.all(
      not_existing_ids.map(async (eventId: any) => {
        const calendarResponse: any = await calendar.events.get({
          calendarId: "primary",
          eventId: eventId,
        });
        calendarResponse.data.therapistId = therapistId;
        return calendarResponse.data;
      })
    );

    const all_uids = events.map((event) => event.iCalUID);
    const all_uids_unique = Array.from(new Set(all_uids));

    let schedules: any = [];
    let timeZone;
    let name;
    let email;
    for (let uid of all_uids_unique) {
      const uid_events = events.filter((event) => event.iCalUID == uid);
      const first_event_data = uid_events[0];

      const clientEmails = await first_event_data.attendees
        .filter(
          (attendee: any) => String(attendee.email) != String(therapist.email)
        )
        .map((attendee: any) => String(attendee.email));
      const uniqueClientEmails = Array.from(new Set(clientEmails));

      let client: any = undefined;
      for (let email of uniqueClientEmails) {
        const clientExist = await ClientService.getClientByEmail(
          String(email),
          therapistId
        );
        if (clientExist) {
          client = clientExist;
          break;
        }
      }

      if (!client) {
        client = await ClientService.create({
          email: uniqueClientEmails[0],
          therapistId: therapistId,
        });
      }

      timeZone = client?.defaultTimezone || "Asia/Kolkata";
      name = client?.name || "patient";
      email = client?.email;

      if (!client) {
        return throwError("client not created. Something went wrong.", 404);
      }
      const additionalEmails = uniqueClientEmails.filter(
        (email: any) => email != client.email
      );
      const duration = moment(first_event_data.end.dateTime).diff(
        moment(first_event_data.start.dateTime),
        "minutes"
      );

      let tillDate = moment(first_event_data.start.dateTime);
      tillDate = tillDate.add(3, "months");

      let scheduleData = new ScheduleModel({
        therapistId: therapist._id,
        recurrenceDates: [],
        scheduleId: therapist.identifier + "_" + Utility.generateRandomNumber(),
        clientId: client._id,
        additionalEmails: additionalEmails,
        durationOfSchedule: duration,
        // location: first_event_data.location,
        location: "online",
        clientCountry: "India",
        email: client.email,
        name: client?.name || "",
        phone: client?.phone || "",
        age: client?.age || "",
        gender: client?.gender || "",
        tillDate: tillDate || "",
        description: first_event_data.description
          ? first_event_data.description
          : first_event_data.summary,
        summary: first_event_data.summary
          ? first_event_data.summary
          : first_event_data.description,
      });

      // console.log(uid_events);

      // Collect all recurrence dates first
      const allRecurrenceDates = uid_events.map(uid_event => ({
        fromDate: new Date(uid_event.start.dateTime),
        toDate: new Date(uid_event.end.dateTime),
      }));

      // Check for conflicts with all events at once
      const checkifOverlap = isOverlap(allRecurrenceDates, existing_dates);

      if (checkifOverlap) {
        // Find all conflicts
        const conflicts = findOverlaps(allRecurrenceDates, existing_dates);

        // Format the first conflict for the message
        const firstConflict = conflicts[0];
        const conflictDate = moment(firstConflict.startTime).format(
          "YYYY-MM-DD h:mm A"
        );

        let errorMessage = `A conflict was found. You have ${
          conflicts.length
        } existing session${
          conflicts.length > 1 ? "s" : ""
        } in your calendar.`;

        // Add the first conflict date as an example
        errorMessage += ` For example, you have a session on ${conflictDate}.`;

        const error: any = new Error(errorMessage);
        error.status = 400;
        error.conflicts = conflicts;
        throw error;
      }

      // If no conflicts, process all events
      for (let uid_event of uid_events) {
        const saveEvent = await GoogleCalendarService.createEvent(uid_event);
        if (!saveEvent) {
          return throwError("Calendar Event No Saved In DataBase.", 500);
        }
        const reccurrance: any = {
          fromDate: new Date(uid_event.start.dateTime),
          toDate: new Date(uid_event.end.dateTime),
          status: uid_event.status,
          amount: client?.defaultSessionAmount || 0,
          meetLink: uid_event.hangoutLink,
          calenderEventId: saveEvent._id,
          _id: new Types.ObjectId(),
        };
        scheduleData.recurrenceDates.push(reccurrance);
        saveEvent.scheduleRecId = reccurrance._id;
        await saveEvent.save();
      }

      const schedule = await ScheduleDao.createSchedule(scheduleData);

      if (schedule.recurrenceDates.length == 1) {
        schedule.recurrence = "Does Not Repeat";
      } else {
        const firstDate = moment(schedule.recurrenceDates[0].fromDate);
        const secondDate = moment(schedule.recurrenceDates[1].fromDate);

        // Calculate day difference
        const dayDifference = secondDate.diff(firstDate, "days");

        // Get the day of the week for firstDate
        const dayOfWeek = firstDate.format("dddd"); // e.g., "Monday"

        if (dayDifference == 1) {
          schedule.recurrence = "Every Day";
        } else if (dayDifference == 7) {
          schedule.recurrence = `Every Week On ${dayOfWeek}`;
        } else {
          schedule.recurrence = `Every Two Weeks ${dayOfWeek}`;
        }
      }
      schedule.save();
      // Create PayTrackers for each recurrence
      for (let recurrence of schedule.recurrenceDates) {
        const paymentTracker: any = await PayTrackerService.createPayTracker({
          therapistId: therapist._id,
          scheduleId: schedule._id, // Use the created schedule's ID
          scheduleRecId: recurrence._id, // Use the recurrence ID
          clientId: client._id,
          dueDate: recurrence.fromDate || "",
          amount: {
            currency: "INR",
            value: recurrence.amount,
          },
          paymentType: moment(recurrence.fromDate).isBefore(moment())
            ? PaymentTrackerTypeEnum.Advance
            : PaymentTrackerTypeEnum.Post_Session,
          status: PaymentTrackerStatusEnum.Still_Pending,
          paymentDate: undefined,
          isDeleted: false,
          tags: [],
          sendRemainder: 0,
          isFine: false,
          cancellationFee: {
            currency: "INR",
            value: 0,
          },
        });

        // console.log(paymentTracker);

        if (!paymentTracker) {
          return throwError("Payment Tracker Not Created.", 500);
        }

        await calendarEventModel.findByIdAndUpdate(
          recurrence.calenderEventId,
          {
            $set: {
              location: "online",
              scheduleId: schedule._id,
            },
          },
          {
            new: true,
          }
        );

        recurrence.payTrackerId = paymentTracker._id;
      }
      schedule.save();
      schedules.push(schedule);

      let htmlTemplate = createScheduleTemplate({
        clientName: schedule.name || "There",
        therapistName: therapist.name || "",
        scheduleDate: schedule?.recurrenceDates[0]?.fromDate || "",
        meetingLink: schedule?.recurrenceDates[0]?.meetLink || "",
        // paymentLink: "",
        // payLater: true,
        timezone: timeZone,
        amount: schedule?.recurrenceDates[0]?.amount || 0,
      });

      let senderData = {
        email: therapist.email,
        name: therapist.name,
      };

      let receiverData = [
        {
          email: email,
          name: name || "There",
        },
      ];

      let subject = MailSubjectEnum.REMAINDER;

      // Send email notification
      await Mailer2.sendMail(senderData, receiverData, subject, htmlTemplate);
    }

    return {
      schedules: schedules,
    };
  }

  static async syncCalendarClientsLocally(
    therapistId: string,
    events: { calendarEventId: string; summary: string; attendee: string }[]
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    if (!googleCalendarData) {
      return throwError("No Google Calendar Data found.", 404);
    }

    const therapist = await TherapistDao.getTherapist(therapistId);
    if (!therapist) {
      return throwError("No Therapist found.", 404);
    }

    let clientsWithEvents = [];
    let inactiveClients: string[] = [];

    // **First, check all clients**
    for (let event of events) {
      const clientExist = await ClientService.getClientByEmail(
        event.attendee,
        therapistId
      );

      if (clientExist) {
        if (clientExist.isActive === false) {
          inactiveClients.push(event.attendee);
        }

        clientsWithEvents.push({
          calendarEventId: event.calendarEventId,
          summary: event.summary,
          attendee: event.attendee,
          exists: true,
          client: {
            id: clientExist._id,
            name: clientExist.name,
            mobileNumber: clientExist.phone,
            Amount: clientExist.defaultSessionAmount,
            isActive: clientExist.isActive,
          },
        });
      } else {
        clientsWithEvents.push({
          calendarEventId: event.calendarEventId,
          summary: event.summary,
          attendee: event.attendee,
          exists: false,
          client: null,
        });
      }
    }

    // **If any client is inactive, stop syncing and return error**
    if (inactiveClients.length > 0) {
      return {
        success: false,
        message: `Please remove Inactive
          clients sessions to sync.
          ${inactiveClients.join("\n")}`,
        inactiveClients,
      };
    }

    //Sort out clients so that client that do not exist show first.
    clientsWithEvents.sort((a, b) => Number(a.exists) - Number(b.exists));
    // **Check if all clients exist before syncing**
    const syncable = clientsWithEvents.every((event) => event.exists);

    return {
      success: true,
      syncable: syncable,
      events: clientsWithEvents,
    };
  }

  static async resyncCalendarEvents(therapistId: any, maxResults?: number) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );

    if (!googleCalendarData) {
      throwError("No Google Calender Data found.", 404);
      return {
        syncable: [],
        not_syncable: [],
      };
    }
    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    if (!tokens) {
      throwError("No Google Calender Data found.", 404);
      return {
        syncable: [],
        not_syncable: [],
      };
    }

    oauth2Client.setCredentials(tokens);
    const calendar = google.calendar({ version: "v3", auth: oauth2Client });

    const calenderEventsParams: any = {
      calendarId: "primary",
      timeMin: new Date().toISOString(),
      maxResults: maxResults, // Change this value if you want to fetch more or fewer events
      singleEvents: true,
      orderBy: "startTime",
    };

    if (maxResults) {
      calenderEventsParams["maxResults"] = maxResults; // Change this value if you want to fetch more or fewer events
    } else {
      calenderEventsParams["timeMax"] = moment().add(3, "months").toISOString();
    }

    // const calendarResponse = await calendar.events.list(calenderEventsParams);

    let allEvents: any[] = [];
    let pageToken: string | undefined = undefined;
    do {
      const calendarResponse: any = await calendar.events.list({
        ...calenderEventsParams,
        pageToken,
      });
      allEvents = allEvents.concat(calendarResponse.data.items || []);
      pageToken = calendarResponse.data.nextPageToken;
    } while (pageToken);

    const events: any = allEvents;

    // 1. Get all calendar events from DB for this therapist
    const dbEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);
    const dbEventIds = dbEvents.map((event: any) => event.id.split('_')[0]);

    // 2. Build a set of all start-end pairs from DB
    const dbStartEndSet = new Set(
      dbEvents.map(ev =>
        `${new Date(ev.start.dateTime).toISOString()}|${new Date(ev.end.dateTime).toISOString()}`
      )
    );

  

    const eventsWithRescheduleFlag = events.map((event: any) => {
      const start = new Date(event.start.dateTime).toISOString();
      const end = new Date(event.end.dateTime).toISOString();
      const key = `${start}|${end}`;
    
      // Extract eventId without timestamp
      const idWithoutTimestamp = event.id?.split('_')[0];
    
      // Extract eventId from description (after '@@@')
      let eventIdFromDescription = null;
      if (typeof event.description === 'string' && event.description.includes('@@@')) {
        eventIdFromDescription = event.description.split('@@@')[1]?.trim();
      }
    
      // Condition for reschedule
      const isRescheduled =
        (!dbStartEndSet.has(key) && dbEventIds.includes(event.id.split('_')[0])) ||
        (idWithoutTimestamp && eventIdFromDescription && idWithoutTimestamp !== eventIdFromDescription && (!dbStartEndSet.has(key)))
          
    
      if (isRescheduled) {
        return { ...event, isRescheduled: true };
      }
      return event;
    });

    const rescheduledEventIds = eventsWithRescheduleFlag
    .filter((event: any) => event.isRescheduled)
    .map((event: any) => event.id);

    if (!eventsWithRescheduleFlag || eventsWithRescheduleFlag.length == 0) {
      return {
        syncable: [],
        not_syncable: [],
      };
    }

    let event_ids = eventsWithRescheduleFlag.map((event: any) => event.id);
    let event_ids_unique = Array.from(new Set(event_ids));

    // let event_icallIds = events.map((event) => event.iCalUID);
    // let event_icallIds_unique = Array.from(new Set(event_icallIds));

    let existing_events = await CalendarEventDao.getTherapistCalendarEvents(
      therapistId
    );

    let existing_event_ids = existing_events.map((event) => event.id);

    let not_existing_ids = event_ids_unique.filter(
      (id: any) => !existing_event_ids.includes(id)
    );

    // Add rescheduledEventIds to not_existing_ids and make unique
    not_existing_ids = Array.from(new Set([...not_existing_ids, ...rescheduledEventIds]));

    // Remove any rescheduledEventIds from existing_event_ids
    existing_event_ids = existing_event_ids.filter((id: any) => !rescheduledEventIds.includes(id));

    let all_non_synced_events: any[] = [];

    for (let not_existing_id of not_existing_ids) {
      const event_details = eventsWithRescheduleFlag.find((event: any) => event.id == not_existing_id);

      if (!event_details) {
        continue;
      }

      const checkifinArray = all_non_synced_events.find(
        (eve: any) => eve.iCalUID == event_details.iCalUID
      );
      if (checkifinArray) {
        continue;
      }

      let all_not_existing_events = eventsWithRescheduleFlag.filter(
        (event: any) =>
          event.iCalUID == event_details.iCalUID &&
          !existing_event_ids.includes(event.id)
      );

      // let all_not_existing_events = events.filter((event) => event.iCalUID == not_existing_id);
      let event: any = all_not_existing_events[0];
      event["eventOccurences"] = all_not_existing_events.length;
      event["eventIds"] = all_not_existing_events.map((event: any) => event.id);
      all_non_synced_events.push(event);
    }

    const syncable = all_non_synced_events.filter(
      (eve) => eve.attendees && eve.attendees.length > 1
    );
    const not_syncable = all_non_synced_events.filter((eve) => !eve.attendees);

    return {
      syncable: syncable,
      not_syncable: not_syncable,
    };
  }

  static async eventByDate(
    therapistId: any,
    maxResults: number,
    startDate: any,
    endDate: any
  ) {
    let startingDate = new Date(startDate);
    let endingDate = new Date(endDate);
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    if (!googleCalendarData) {
      throwError("No Google Calender Data found.", 404);
      return [];
    }
    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );
    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };
    if (!tokens) {
      throwError("No Google Calender Data found.", 404);
      return {
        syncable: [],
        not_syncable: [],
      };
    }
    oauth2Client.setCredentials(tokens);
    const calendar = google.calendar({ version: "v3", auth: oauth2Client });
    const calendarResponse: any = await calendar.events.list({
      calendarId: "primary",
      timeMin: startingDate.toISOString(),
      timeMax: endingDate.toISOString(),
      maxResults: maxResults, // Change this value if you want to fetch more or fewer events
      singleEvents: true,
      orderBy: "startTime",
    });
    const events = calendarResponse?.data?.items;
    return events;
  }

  // Find events by therapist and dates to avoid duplicates
  static async findEventsByTherapistAndDates(
    therapistId: string,
    dates: { fromDate: Date; toDate: Date }[]
  ) {
    try {
      // Get Google Calendar credentials
      const googleCalendarData = await GoogleCalendarService.findByTherapist(
        therapistId
      );
      if (!googleCalendarData) {
        return [];
      }

      // Set up OAuth client for Google Calendar API
      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );
      oauth2Client.setCredentials({
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token,
      });

      const calendar = google.calendar({ version: "v3", auth: oauth2Client });

      // Find events for each date range
      const existingEvents = [];

      for (const dateRange of dates) {
        try {
          // Convert dates to ISO strings for Google Calendar API
          const timeMin = moment(dateRange.fromDate)
            .subtract(5, "minutes")
            .toISOString();
          const timeMax = moment(dateRange.toDate)
            .add(5, "minutes")
            .toISOString();

          // Search for events in this time range
          const response = await calendar.events.list({
            calendarId: "primary",
            timeMin: timeMin,
            timeMax: timeMax,
            singleEvents: true,
          });

          if (response.data.items && response.data.items.length > 0) {
            existingEvents.push(...response.data.items);
          }
        } catch (error) {
          console.error(`Error searching for events in date range:`, error);
        }
      }

      return existingEvents;
    } catch (error) {
      console.error("Error finding events by therapist and dates:", error);
      return [];
    }
  }

  static async updateEventTime(
    therapistId: any,
    calendarId: any,
    date: { fromDate: any; toDate: any }
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    if (!tokens) {
      throwError("No Google Calender Data found.", 404);
      return {
        syncable: [],
        not_syncable: [],
      };
    }

    oauth2Client.setCredentials(tokens);

    const event = await GoogleCalendarService.findby_id(calendarId);
    if (!event) {
      return throwError("Calendar Event not found", 404);
    }
    // console.log("event attendees : ",event.attendees);
    const payload = {
      start: {
        dateTime: date.fromDate,
        timeZone: event.start.timeZone,
      },
      end: {
        dateTime: date.toDate,
        timeZone: event.end.timeZone,
      },
      summary: event.summary,
      description: event.description,
      attendees: event.attendees,
    };

    const calendar: any = google.calendar({
      version: "v3",
      auth: oauth2Client,
    });

    try {
      const eventData = await calendar.events.get({
        calendarId: "primary",
        eventId: event._id,
      });

      const newEvent = {
        ...eventData.data,
        ...payload,
      };

      // console.log(JSON.stringify(newEvent), "new Event");

      const response = await calendar.events.update({
        calendarId: "primary",
        eventId: event.id,
        resource: newEvent,
      });
      // console.log("Response ",response.data);
      event.id = response.data.id;
      event.start = response.data.start;
      event.end = response.data.end;

      return await event.save();
    } catch (error) {
      console.log(error);
      return throwError("Unable to update google calendar", 400);
    }
  }

  static async isTimeSlotBusy(
    therapistId: any,
    date: string, // DD-MM-YYYY
    timeSlots: { startTime: string; endTime: string; duration: number }[]
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );

    if (!googleCalendarData) {
      return {
        status: false,
        statusCode: 404,
        message: "No Google Calendar data found.",
      };
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token,
    };

    if (!tokens.access_token || !tokens.refresh_token) {
      return {
        status: false,
        statusCode: 404,
        message: "Google Calendar tokens are missing.",
      };
    }

    oauth2Client.setCredentials(tokens);

    const calendar = google.calendar({ version: "v3", auth: oauth2Client });

    const busySlots: any[] = [];

    for (const slot of timeSlots) {
      // Validate slot times
      if (!slot.startTime || !slot.endTime) {
        continue;
      }

      // Parse the date in DD-MM-YYYY format
      const [day, month, year] = date.split("-");
      const formattedDate = `${year}-${month}-${day}`;

      // Combine date with time and create ISO string with correct timezone
      const startDateTime = moment.tz(
        `${formattedDate} ${slot.startTime}`,
        "YYYY-MM-DD HH:mm",
        "Asia/Kolkata"
      );

      const endDateTime = moment.tz(
        `${formattedDate} ${slot.endTime}`,
        "YYYY-MM-DD HH:mm",
        "Asia/Kolkata"
      );

      try {
        const events: any = await calendar.events.list({
          calendarId: "primary",
          timeMin: startDateTime.toISOString(),
          timeMax: endDateTime.toISOString(),
          singleEvents: true,
          orderBy: "startTime",
          timeZone: "Asia/Kolkata",
        });

        if (events.data.items && events.data.items.length > 0) {
          busySlots.push({
            slot,
            conflictingEvent: {
              eventStart: events.data.items[0].start.dateTime,
              eventEnd: events.data.items[0].end.dateTime,
            },
          });
        }
      } catch (error) {
        console.error("Error checking slot:", slot, error);
        return {
          status: false,
          statusCode: 500,
          message:
            "Failed while checking Google Calendar for one or more slots.",
          error,
        };
      }
    }

    if (busySlots.length > 0) {
      return {
        status: true,
        busy: true,
        message: "Some time slots are busy.",
        busySlots,
      };
    }

    return {
      status: true,
      busy: false,
      message: "All time slots are free.",
    };
  }

  static async updateEventDescriptions(therapistId: any, eventIds: any) {
    try {
      const googleCalendarData = await GoogleCalendarService.findByTherapist(
        therapistId
      );
  
      if (!googleCalendarData) {
        throwError("No Google Calender Data found.", 404);
        return;
      }
      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );
  
      const tokens = {
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token,
      };
  
      if (!tokens) {
        throwError("No Google Calender Data found.", 404);
        return;
      }
  
      oauth2Client.setCredentials(tokens);
      const calendar = google.calendar({ version: "v3", auth: oauth2Client });
  
      const calendarId = 'primary';

      const parentEventId = eventIds[0].split('_')[0];

      // Get the parent event
      const { data: parentEvent } = await calendar.events.get({
        calendarId,
        eventId: parentEventId
      });

      // Use existing description or empty string
      let currentDescription = parentEvent.description || '';

      // Replace the value after '@@@' if already present, otherwise append
      const updatedDescription = currentDescription.includes('@@@')
        ? currentDescription.replace(/@@@.*/g, `@@@${parentEventId}`)
        : `${currentDescription}@@@${parentEventId}`;

      // Update the parent event only
      await calendar.events.patch({
        calendarId,
        eventId: parentEventId,
        requestBody: {
          description: updatedDescription
        }
      });
      return;
    } catch (err: any) {
      console.error("Error updating events description", err.message);
      return;
    }
  }
}
